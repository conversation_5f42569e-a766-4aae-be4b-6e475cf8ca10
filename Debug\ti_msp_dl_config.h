/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 */

/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)
#define CPUCLK_FREQ                                                     32000000

/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                           32000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                (115200)
#define UART_0_IBRD_32_MHZ_115200_BAUD                                      (17)
#define UART_0_FBRD_32_MHZ_115200_BAUD                                      (23)

/* Defines for ADC12_0 */
#define ADC12_0_INST                                                        ADC0
#define ADC12_0_INST_IRQHandler                                  ADC0_IRQHandler
#define ADC12_0_INST_INT_IRQN                                    (ADC0_INT_IRQn)
#define ADC12_0_ADCMEM_0                                      DL_ADC12_MEM_IDX_0
#define ADC12_0_ADCMEM_0_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_0_REF_VOLTAGE_V                                       3.3
#define GPIO_ADC12_0_C0_PORT                                               GPIOA
#define GPIO_ADC12_0_C0_PIN                                       DL_GPIO_PIN_27

/* Defines for PWM_MOTOR - PWM定时器 */
#define PWM_MOTOR_INST                                                     TIMG0
#define PWM_MOTOR_INST_IRQHandler                               TIMG0_IRQHandler
#define PWM_MOTOR_INST_INT_IRQN                                   TIMG0_INT_IRQn
#define GPIO_PWM_MOTOR_C0_PORT                                             GPIOA
#define GPIO_PWM_MOTOR_C0_PIN                                     DL_GPIO_PIN_14
#define GPIO_PWM_MOTOR_C1_PORT                                             GPIOA
#define GPIO_PWM_MOTOR_C1_PIN                                     DL_GPIO_PIN_15
#define PWM_MOTOR_C0_IOMUX                                       (IOMUX_PINCM36)
#define PWM_MOTOR_C1_IOMUX                                       (IOMUX_PINCM37)
#define PWM_MOTOR_C0_IOMUX_FUNC                        IOMUX_PINCM36_PF_TIMG0_CCP0
#define PWM_MOTOR_C1_IOMUX_FUNC                        IOMUX_PINCM37_PF_TIMG0_CCP1

/* Defines for ENCODER1A - 编码器1输入捕获 */
#define ENCODER1A_INST                                                     TIMA0
#define ENCODER1A_INST_IRQHandler                               TIMA0_IRQHandler
#define ENCODER1A_INST_INT_IRQN                                   TIMA0_INT_IRQn
#define GPIO_ENCODER1A_C0_PORT                                             GPIOB
#define GPIO_ENCODER1A_C0_PIN                                      DL_GPIO_PIN_7
#define ENCODER1A_C0_IOMUX                                       (IOMUX_PINCM18)
#define ENCODER1A_C0_IOMUX_FUNC                        IOMUX_PINCM18_PF_TIMA0_CCP0

/* Defines for ENCODER2A - 编码器2输入捕获 */
#define ENCODER2A_INST                                                     TIMG6
#define ENCODER2A_INST_IRQHandler                               TIMG6_IRQHandler
#define ENCODER2A_INST_INT_IRQN                                   TIMG6_INT_IRQn
#define GPIO_ENCODER2A_C0_PORT                                             GPIOB
#define GPIO_ENCODER2A_C0_PIN                                      DL_GPIO_PIN_8
#define ENCODER2A_C0_IOMUX                                       (IOMUX_PINCM19)
#define ENCODER2A_C0_IOMUX_FUNC                        IOMUX_PINCM19_PF_TIMG6_CCP0

/* Defines for TIMER_CONTROL - 电机控制周期定时器 */
#define TIMER_CONTROL_INST                                                 TIMA1
#define TIMER_CONTROL_INST_IRQHandler                           TIMA1_IRQHandler
#define TIMER_CONTROL_INST_INT_IRQN                               TIMA1_INT_IRQn

/* Port definition for Pin Group LED */
#define LED_PORT                                                         (GPIOB)
#define LED_PIN_22_PIN                                          (DL_GPIO_PIN_22)
#define LED_PIN_22_IOMUX                                         (IOMUX_PINCM50)

/* Port definition for Pin Group AIN */
#define AIN_PORT                                                         (GPIOA)
#define AIN_AIN1_PIN                                            (DL_GPIO_PIN_13)
#define AIN_AIN1_IOMUX                                           (IOMUX_PINCM35)
#define AIN_AIN2_PIN                                            (DL_GPIO_PIN_12)
#define AIN_AIN2_IOMUX                                           (IOMUX_PINCM34)

/* Port definition for Pin Group Gray_Address */
#define Gray_Address_PORT                                                (GPIOB)
#define Gray_Address_PIN_0_PIN                                   (DL_GPIO_PIN_0)
#define Gray_Address_PIN_0_IOMUX                                 (IOMUX_PINCM12)
#define Gray_Address_PIN_1_PIN                                   (DL_GPIO_PIN_1)
#define Gray_Address_PIN_1_IOMUX                                 (IOMUX_PINCM13)
#define Gray_Address_PIN_2_PIN                                   (DL_GPIO_PIN_2)
#define Gray_Address_PIN_2_IOMUX                                 (IOMUX_PINCM15)

/* Port definition for Pin Group GPIO_MOTOR - 电机方向控制 */
#define GPIO_MOTOR_PORT                                                  (GPIOB)
#define GPIO_MOTOR_AIN1_PIN                                      (DL_GPIO_PIN_3)
#define GPIO_MOTOR_AIN1_IOMUX                                    (IOMUX_PINCM16)
#define GPIO_MOTOR_AIN2_PIN                                      (DL_GPIO_PIN_4)
#define GPIO_MOTOR_AIN2_IOMUX                                    (IOMUX_PINCM17)
#define GPIO_MOTOR_BIN1_PIN                                      (DL_GPIO_PIN_5)
#define GPIO_MOTOR_BIN1_IOMUX                                    (IOMUX_PINCM18)
#define GPIO_MOTOR_BIN2_PIN                                      (DL_GPIO_PIN_6)
#define GPIO_MOTOR_BIN2_IOMUX                                    (IOMUX_PINCM19)

/* Port definition for Pin Group GPIO_ENCODER - 编码器B相 */
#define GPIO_ENCODER_PORT                                                (GPIOB)
#define GPIO_ENCODER_ENCODER1B_PIN                               (DL_GPIO_PIN_9)
#define GPIO_ENCODER_ENCODER1B_IOMUX                             (IOMUX_PINCM20)
#define GPIO_ENCODER_ENCODER2B_PIN                              (DL_GPIO_PIN_10)
#define GPIO_ENCODER_ENCODER2B_IOMUX                             (IOMUX_PINCM21)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_ADC12_0_init(void);
void SYSCFG_DL_PWM_MOTOR_init(void);
void SYSCFG_DL_ENCODER1A_init(void);
void SYSCFG_DL_ENCODER2A_init(void);
void SYSCFG_DL_TIMER_CONTROL_init(void);
void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
