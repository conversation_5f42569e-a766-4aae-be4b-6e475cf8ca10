/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12   = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121  = ADC12.addInstance();
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();  // 电机方向控制GPIO
const GPIO5   = GPIO.addInstance();  // 编码器B相GPIO
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance(); // PWM定时器
const TIMER2  = TIMER.addInstance(); // 编码器输入捕获定时器1
const TIMER3  = TIMER.addInstance(); // 编码器输入捕获定时器2
const TIMER4  = TIMER.addInstance(); // 电机控制周期定时器
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
ADC121.$name               = "ADC12_0";
ADC121.sampClkDiv          = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.powerDownMode       = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.adcPin0Config.$name = "ti_driverlib_gpio_GPIOPinGeneric2";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.port                          = "PORTB";
GPIO1.$name                         = "LED";
GPIO1.associatedPins[0].$name       = "PIN_22";
GPIO1.associatedPins[0].assignedPin = "22";

GPIO2.$name                   = "AIN";
GPIO2.port                    = "PORTA";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].$name = "AIN1";
GPIO2.associatedPins[1].$name = "AIN2";

GPIO3.$name                         = "Gray_Address";
GPIO3.port                          = "PORTB";
GPIO3.associatedPins.create(3);
GPIO3.associatedPins[0].$name       = "PIN_0";
GPIO3.associatedPins[0].pin.$assign = "PB0";
GPIO3.associatedPins[1].$name       = "PIN_1";
GPIO3.associatedPins[1].pin.$assign = "PB1";
GPIO3.associatedPins[2].$name       = "PIN_2";
GPIO3.associatedPins[2].pin.$assign = "PB2";

// 电机方向控制GPIO配置
GPIO4.$name                         = "GPIO_MOTOR";
GPIO4.port                          = "PORTB";
GPIO4.associatedPins.create(4);
GPIO4.associatedPins[0].$name       = "AIN1";
GPIO4.associatedPins[0].pin.$assign = "PB3";
GPIO4.associatedPins[1].$name       = "AIN2";
GPIO4.associatedPins[1].pin.$assign = "PB4";
GPIO4.associatedPins[2].$name       = "BIN1";
GPIO4.associatedPins[2].pin.$assign = "PB5";
GPIO4.associatedPins[3].$name       = "BIN2";
GPIO4.associatedPins[3].pin.$assign = "PB6";

// 编码器B相GPIO配置
GPIO5.$name                         = "GPIO_ENCODER";
GPIO5.port                          = "PORTB";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name       = "ENCODER1B";
GPIO5.associatedPins[0].pin.$assign = "PB9";
GPIO5.associatedPins[0].direction   = "INPUT";
GPIO5.associatedPins[1].$name       = "ENCODER2B";
GPIO5.associatedPins[1].pin.$assign = "PB10";
GPIO5.associatedPins[1].direction   = "INPUT";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable      = true;
SYSTICK.period            = 32000;
SYSTICK.interruptEnable   = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptPriority = "0";

// PWM定时器配置 - 32kHz频率
TIMER1.$name                      = "PWM_MOTOR";
TIMER1.timerMode                  = "PERIODIC";
TIMER1.timerPeriod                = "31.25 us";
TIMER1.pwmMode                    = true;
TIMER1.ccIndex                    = [0, 1];
TIMER1.peripheral.$assign         = "TIMG0";
TIMER1.peripheral.ccp0Pin.$assign = "PA14";
TIMER1.peripheral.ccp1Pin.$assign = "PA15";
TIMER1.PWM_CHANNEL_0.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";
TIMER1.PWM_CHANNEL_1.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";

// 编码器1输入捕获定时器配置
TIMER2.$name                      = "ENCODER1A";
TIMER2.timerMode                  = "PERIODIC";
TIMER2.captureMode                = true;
TIMER2.ccIndex                    = [0];
TIMER2.captureInputMode           = "RISING_EDGE";
TIMER2.enabledInterrupts          = ["CC0_DN"];
TIMER2.interruptPriority          = "2";
TIMER2.peripheral.$assign         = "TIMA0";
TIMER2.peripheral.ccp0Pin.$assign = "PB7";
TIMER2.CCP0.$name                 = "ti_driverlib_gpio_GPIOPinGeneric5";

// 编码器2输入捕获定时器配置
TIMER3.$name                      = "ENCODER2A";
TIMER3.timerMode                  = "PERIODIC";
TIMER3.captureMode                = true;
TIMER3.ccIndex                    = [0];
TIMER3.captureInputMode           = "RISING_EDGE";
TIMER3.enabledInterrupts          = ["CC0_DN"];
TIMER3.interruptPriority          = "2";
TIMER3.peripheral.$assign         = "TIMG6";
TIMER3.peripheral.ccp0Pin.$assign = "PB8";
TIMER3.CCP0.$name                 = "ti_driverlib_gpio_GPIOPinGeneric6";

// 电机控制周期定时器配置 - 100Hz
TIMER4.$name                      = "TIMER_CONTROL";
TIMER4.timerMode                  = "PERIODIC";
TIMER4.timerPeriod                = "10 ms";
TIMER4.enabledInterrupts          = ["ZERO"];
TIMER4.interruptPriority          = "1";
TIMER4.peripheral.$assign         = "TIMA1";

UART1.$name                    = "UART_0";
UART1.interruptPriority        = "1";
UART1.targetBaudRate           = 115200;
UART1.rxFifoThreshold          = "DL_UART_RX_FIFO_LEVEL_ONE_ENTRY";
UART1.enabledInterrupts        = ["RX"];
UART1.enableDMARX              = false;
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.peripheral.$suggestSolution           = "ADC0";
ADC121.peripheral.adcPin0.$suggestSolution   = "PA27";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution = "PB22";
GPIO2.associatedPins[0].pin.$suggestSolution = "PA13";
GPIO2.associatedPins[1].pin.$suggestSolution = "PA12";
GPIO4.associatedPins[0].pin.$suggestSolution = "PB3";
GPIO4.associatedPins[1].pin.$suggestSolution = "PB4";
GPIO4.associatedPins[2].pin.$suggestSolution = "PB5";
GPIO4.associatedPins[3].pin.$suggestSolution = "PB6";
GPIO5.associatedPins[0].pin.$suggestSolution = "PB9";
GPIO5.associatedPins[1].pin.$suggestSolution = "PB10";
TIMER1.peripheral.$suggestSolution           = "TIMG0";
TIMER1.peripheral.ccp0Pin.$suggestSolution   = "PA14";
TIMER1.peripheral.ccp1Pin.$suggestSolution   = "PA15";
TIMER2.peripheral.$suggestSolution           = "TIMA0";
TIMER2.peripheral.ccp0Pin.$suggestSolution   = "PB7";
TIMER3.peripheral.$suggestSolution           = "TIMG6";
TIMER3.peripheral.ccp0Pin.$suggestSolution   = "PB8";
TIMER4.peripheral.$suggestSolution           = "TIMA1";
